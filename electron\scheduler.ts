import { getScheduledTasks, executeBackup } from './database';

class BackupScheduler {
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;
  private lastExecutionTime: { [taskId: number]: string } = {};

  // 启动调度器
  start() {
    if (this.isRunning) {
      console.log('Backup scheduler is already running');
      return;
    }

    console.log('Starting backup scheduler...');
    this.isRunning = true;
    
    // 每分钟检查一次
    this.intervalId = setInterval(() => {
      this.checkAndExecuteTasks();
    }, 60000); // 60秒

    // 立即执行一次检查
    this.checkAndExecuteTasks();
  }

  // 停止调度器
  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isRunning = false;
    console.log('Backup scheduler stopped');
  }

  // 检查并执行任务
  private async checkAndExecuteTasks() {
    try {
      const result = await getScheduledTasks();
      
      if (!result.success || !result.tasks) {
        return;
      }

      const now = new Date();
      const currentTimeKey = now.toISOString().slice(0, 16); // YYYY-MM-DDTHH:MM

      for (const task of result.tasks) {
        const taskId = task.id;
        
        // 检查是否已在当前时间执行过
        if (this.lastExecutionTime[taskId] === currentTimeKey) {
          continue;
        }

        // 检查是否应该执行
        if (this.shouldExecuteTask(task, now)) {
          console.log(`Executing scheduled backup task: ${task.task_name} (ID: ${taskId})`);
          
          try {
            const backupResult = await executeBackup(taskId);
            
            if (backupResult.success) {
              console.log(`Scheduled backup task executed successfully: ${task.task_name}`);
              this.lastExecutionTime[taskId] = currentTimeKey;
            } else {
              console.error(`定时备份任务执行失败: ${task.task_name}`, backupResult.message);
            }
          } catch (error) {
            console.error(`定时备份任务执行异常: ${task.task_name}`, error);
          }
        }
      }
    } catch (error) {
      console.error('检查定时任务失败:', error);
    }
  }

  // 判断是否应该执行任务
  private shouldExecuteTask(task: any, now: Date): boolean {
    const currentTime = now.toTimeString().slice(0, 5); // HH:MM
    const currentDay = now.getDay(); // 0=周日, 1=周一, ..., 6=周六
    const currentDate = now.getDate(); // 1-31

    switch (task.schedule_type) {
      case 'hourly':
        // 每小时在指定分钟执行（如果没有指定时间，默认在每小时的0分钟执行）
        const targetMinute = task.schedule_time ? parseInt(task.schedule_time.split(':')[1]) : 0;
        return now.getMinutes() === targetMinute;

      case 'daily':
        return task.schedule_time === currentTime;

      case 'weekly':
        return task.schedule_time === currentTime && task.schedule_day === currentDay;

      case 'monthly':
        return task.schedule_time === currentTime && task.schedule_day === currentDate;

      default:
        return false;
    }
  }

  // 获取调度器状态
  getStatus() {
    return {
      isRunning: this.isRunning,
      lastExecutionTimes: { ...this.lastExecutionTime }
    };
  }

  // 清除执行历史
  clearExecutionHistory() {
    this.lastExecutionTime = {};
    console.log('Execution history cleared');
  }
}

// 创建全局调度器实例
export const backupScheduler = new BackupScheduler();

// 导出类型
export type { BackupScheduler };
