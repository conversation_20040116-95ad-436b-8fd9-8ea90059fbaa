"use strict";
const electron = require("electron");
electron.contextBridge.exposeInMainWorld("electronAPI", {
  // 用户认证相关API
  userRegister: (userData) => electron.ipcRenderer.invoke("user-register", userData),
  userLogin: (credentials) => electron.ipcRenderer.invoke("user-login", credentials),
  // 服务器管理API
  addServer: (serverData) => electron.ipcRenderer.invoke("add-server", serverData),
  getServers: (userId) => electron.ipcRenderer.invoke("get-servers", userId),
  deleteServer: (serverId, userId) => electron.ipcRenderer.invoke("delete-server", { serverId, userId }),
  getServer: (serverId, userId) => electron.ipcRenderer.invoke("get-server", { serverId, userId }),
  updateServer: (serverId, userId, serverData) => electron.ipcRenderer.invoke("update-server", { serverId, userId, serverData }),
  testServerConnection: (serverConfig) => electron.ipcRenderer.invoke("test-server-connection", serverConfig),
  updateServerStatus: (serverId, status) => electron.ipcRenderer.invoke("update-server-status", serverId, status),
  // 备份任务管理API
  createBackupTask: (taskData) => electron.ipcRenderer.invoke("create-backup-task", taskData),
  getBackupTasks: (userId) => electron.ipcRenderer.invoke("get-backup-tasks", userId),
  deleteBackupTask: (taskId, userId) => electron.ipcRenderer.invoke("delete-backup-task", { taskId, userId }),
  // 备份历史API
  getBackupHistory: (userId, limit) => electron.ipcRenderer.invoke("get-backup-history", userId, limit),
  getBackupTaskHistory: (taskId, userId) => electron.ipcRenderer.invoke("get-backup-task-history", taskId, userId),
  generateCompleteBackupSQL: (taskId, targetHistoryId, userId) => electron.ipcRenderer.invoke("generate-complete-backup-sql", taskId, targetHistoryId, userId),
  executeBackup: (taskId) => electron.ipcRenderer.invoke("execute-backup", taskId),
  getBackupFilePath: (historyId, userId) => electron.ipcRenderer.invoke("get-backup-file-path", historyId, userId),
  showItemInFolder: (filePath) => electron.ipcRenderer.invoke("show-item-in-folder", filePath),
  // 连接测试API
  testDatabaseExists: (serverConfig, databaseName) => electron.ipcRenderer.invoke("test-database-exists", { serverConfig, databaseName }),
  testDatabaseConnection: (serverId, databaseName, userId) => electron.ipcRenderer.invoke("test-database-connection", serverId, databaseName, userId),
  // 系统信息API
  getSystemInfo: (userId) => electron.ipcRenderer.invoke("get-system-info", userId),
  getSystemMetrics: () => electron.ipcRenderer.invoke("get-system-metrics"),
  getBackupStats: (userId) => electron.ipcRenderer.invoke("get-backup-stats", userId),
  // 服务器端备份文件API
  getServerBackupFiles: (serverId, userId) => electron.ipcRenderer.invoke("get-server-backup-files", serverId, userId),
  downloadServerBackupFile: (serverId, filePath, userId) => electron.ipcRenderer.invoke("download-server-backup-file", serverId, filePath, userId),
  // 通用IPC方法
  on(...args) {
    const [channel, listener] = args;
    return electron.ipcRenderer.on(channel, (event, ...args2) => listener(event, ...args2));
  },
  off(...args) {
    const [channel, ...omit] = args;
    return electron.ipcRenderer.off(channel, ...omit);
  },
  send(...args) {
    const [channel, ...omit] = args;
    return electron.ipcRenderer.send(channel, ...omit);
  },
  invoke(...args) {
    const [channel, ...omit] = args;
    return electron.ipcRenderer.invoke(channel, ...omit);
  }
});
