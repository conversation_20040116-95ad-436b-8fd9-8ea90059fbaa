import { app, BrowserWindow, ipc<PERSON>ain, shell } from 'electron'
import { createRequire } from 'node:module'
import { fileURLToPath } from 'node:url'
import path from 'node:path'
import {
  initDatabase,
  createUserTable,
  createServerTable,
  createBackupTaskTable,
  createBackupHistoryTable,
  registerUser,
  loginUser,
  validateEmail,
  closeDatabase,
  addServer,
  getUserServers,
  deleteServer,
  getServerById,
  updateServer,
  updateServerStatus,
  createBackupTask,
  getUserBackupTasks,
  deleteBackupTask,
  getBackupHistory,
  getBackupTaskHistory,
  generateCompleteBackupSQL,
  validateBackupChainContinuity,
  validateBackupFileIntegrity,
  validateAllBackupFiles,
  testServerConnection,
  testDatabaseExists,
  testDatabaseConnectionById,
  executeBackup,
  getBackupFilePath,
  getServerBackupFiles,
  downloadServerBackupFile,
  restoreBackupToPointInTime,
  validateBackupFileIntegrity,
  repairBackupChain
} from './database'
import os from 'os'
import fs from 'fs'
import { backupScheduler } from './scheduler'

const require = createRequire(import.meta.url)
const __dirname = path.dirname(fileURLToPath(import.meta.url))

// The built directory structure
//
// ├─┬─┬ dist
// │ │ └── index.html
// │ │
// │ ├─┬ dist-electron
// │ │ ├── main.js
// │ │ └── preload.mjs
// │
process.env.APP_ROOT = path.join(__dirname, '..')

// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x
export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']
export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')
export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST

let win: BrowserWindow | null

function createWindow() {
  win = new BrowserWindow({
    icon: path.join(process.env.VITE_PUBLIC, 'electron-vite.svg'),
    webPreferences: {
      preload: path.join(__dirname, 'preload.mjs'),
    },
  })

  // Test active push message to Renderer-process.
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', (new Date).toLocaleString())
  })

  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL)
  } else {
    // win.loadFile('dist/index.html')
    win.loadFile(path.join(RENDERER_DIST, 'index.html'))
  }
}

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', async () => {
  if (process.platform !== 'darwin') {
    await closeDatabase();
    app.quit()
    win = null
  }
})

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// 初始化数据库和IPC处理程序
async function initializeApp() {
  try {
    // 设置IPC处理程序（必须在创建窗口之前）
    setupIpcHandlers();

    // 初始化数据库连接
    initDatabase();
    await createUserTable();
    await createServerTable();
    await createBackupTaskTable();
    await createBackupHistoryTable();
    console.log('Database initialization successful - IPC handlers order fixed');

    // 启动备份调度器
    backupScheduler.start();

    // 创建窗口
    createWindow();
  } catch (error) {
    console.error('应用初始化失败:', error);
  }
}

// 设置IPC处理程序
function setupIpcHandlers() {
  // 用户注册
  ipcMain.handle('user-register', async (event, { username, email, password }) => {
    try {
      // 验证邮箱格式
      if (!validateEmail(email)) {
        return { success: false, message: '邮箱格式不正确' };
      }

      // 验证输入
      if (!username || !email || !password) {
        return { success: false, message: '请填写完整信息' };
      }

      if (username.length < 3) {
        return { success: false, message: '用户名至少需要3个字符' };
      }

      if (password.length < 6) {
        return { success: false, message: '密码至少需要6个字符' };
      }

      const result = await registerUser(username, email, password);
      return result;
    } catch (error) {
      console.error('注册处理失败:', error);
      return { success: false, message: '注册失败，请稍后重试' };
    }
  });

  // 用户登录
  ipcMain.handle('user-login', async (event, { username, password }) => {
    try {
      if (!username || !password) {
        return { success: false, message: '请输入用户名和密码' };
      }

      const result = await loginUser(username, password);
      return result;
    } catch (error) {
      console.error('登录处理失败:', error);
      return { success: false, message: '登录失败，请稍后重试' };
    }
  });

  // 添加服务器
  ipcMain.handle('add-server', async (event, serverData) => {
    try {
      const result = await addServer(serverData);
      return result;
    } catch (error) {
      console.error('添加服务器失败:', error);
      return { success: false, message: '添加服务器失败，请稍后重试' };
    }
  });

  // 获取服务器列表
  ipcMain.handle('get-servers', async (event, userId) => {
    try {
      const result = await getUserServers(userId);
      return result;
    } catch (error) {
      console.error('获取服务器列表失败:', error);
      return { success: false, message: '获取服务器列表失败' };
    }
  });

  // 删除服务器
  ipcMain.handle('delete-server', async (event, { serverId, userId }) => {
    try {
      const result = await deleteServer(serverId, userId);
      return result;
    } catch (error) {
      console.error('删除服务器失败:', error);
      return { success: false, message: '删除服务器失败，请稍后重试' };
    }
  });

  // 创建备份任务
  ipcMain.handle('create-backup-task', async (event, taskData) => {
    try {
      console.log('Creating backup task with data:', taskData);
      const result = await createBackupTask(taskData);
      console.log('Create backup task result:', result);
      return result;
    } catch (error: any) {
      console.error('Create backup task failed:', error);
      return {
        success: false,
        message: `创建备份任务失败: ${error?.message || error?.toString() || '未知错误'}`,
        errorCode: error?.code,
        errorDetails: error
      };
    }
  });

  // 获取备份任务列表
  ipcMain.handle('get-backup-tasks', async (event, userId) => {
    try {
      const result = await getUserBackupTasks(userId);
      return result;
    } catch (error) {
      console.error('获取备份任务列表失败:', error);
      return { success: false, message: '获取备份任务列表失败' };
    }
  });

  // 删除备份任务
  ipcMain.handle('delete-backup-task', async (event, { taskId, userId }) => {
    try {
      const result = await deleteBackupTask(taskId, userId);
      return result;
    } catch (error) {
      console.error('删除备份任务失败:', error);
      return { success: false, message: '删除备份任务失败，请稍后重试' };
    }
  });

  // 获取备份历史
  ipcMain.handle('get-backup-history', async (event, userId, limit) => {
    try {
      const result = await getBackupHistory(userId, limit);
      return result;
    } catch (error) {
      console.error('获取备份历史失败:', error);
      return { success: false, message: '获取备份历史失败' };
    }
  });

  // 获取备份任务详细历史
  ipcMain.handle('get-backup-task-history', async (event, taskId, userId) => {
    try {
      const result = await getBackupTaskHistory(taskId, userId);
      return result;
    } catch (error) {
      console.error('获取备份任务历史失败:', error);
      return { success: false, message: '获取备份任务历史失败' };
    }
  });

  // 生成完整SQL文件
  ipcMain.handle('generate-complete-backup-sql', async (event, taskId, targetHistoryId, userId) => {
    try {
      const result = await generateCompleteBackupSQL(taskId, targetHistoryId, userId);
      return result;
    } catch (error) {
      console.error('生成完整SQL文件失败:', error);
      return { success: false, message: '生成完整SQL文件失败' };
    }
  });

  // 验证备份链完整性
  ipcMain.handle('validate-backup-chain', async (event, taskId, userId) => {
    try {
      const result = await validateBackupChainContinuity(taskId, userId);
      return result;
    } catch (error) {
      console.error('验证备份链完整性失败:', error);
      return { success: false, message: '验证备份链完整性失败' };
    }
  });

  // 验证单个备份文件完整性
  ipcMain.handle('validate-backup-file', async (event, historyId, userId) => {
    try {
      const result = await validateBackupFileIntegrity(historyId, userId);
      return result;
    } catch (error) {
      console.error('验证备份文件完整性失败:', error);
      return { success: false, message: '验证备份文件完整性失败' };
    }
  });

  // 验证任务的所有备份文件
  ipcMain.handle('validate-all-backup-files', async (event, taskId, userId) => {
    try {
      const result = await validateAllBackupFiles(taskId, userId);
      return result;
    } catch (error) {
      console.error('验证所有备份文件失败:', error);
      return { success: false, message: '验证所有备份文件失败' };
    }
  });

  // 获取单个服务器信息
  ipcMain.handle('get-server', async (event, { serverId, userId }) => {
    try {
      const result = await getServerById(serverId, userId);
      return result;
    } catch (error) {
      console.error('获取服务器信息失败:', error);
      return { success: false, message: '获取服务器信息失败' };
    }
  });

  // 更新服务器信息
  ipcMain.handle('update-server', async (event, { serverId, userId, serverData }) => {
    try {
      const result = await updateServer(serverId, userId, serverData);
      return result;
    } catch (error) {
      console.error('更新服务器失败:', error);
      return { success: false, message: '更新服务器失败，请稍后重试' };
    }
  });

  // 测试服务器连接
  ipcMain.handle('test-server-connection', async (event, serverConfig) => {
    try {
      console.log('Received connection test request:', { host: serverConfig.host, port: serverConfig.port });
      const result = await testServerConnection(serverConfig);
      console.log('Connection test result:', result);
      return result;
    } catch (error) {
      console.error('测试服务器连接失败:', error);
      return {
        success: false,
        message: '连接测试失败，请稍后重试',
        details: { errorMessage: error instanceof Error ? error.message : String(error) }
      };
    }
  });

  // 测试数据库是否存在
  ipcMain.handle('test-database-exists', async (event, { serverConfig, databaseName }) => {
    try {
      const result = await testDatabaseExists(serverConfig, databaseName);
      return result;
    } catch (error) {
      console.error('测试数据库存在性失败:', error);
      return { success: false, message: '测试数据库失败，请稍后重试' };
    }
  });

  // 测试数据库连接（通过服务器ID）
  ipcMain.handle('test-database-connection', async (event, serverId, databaseName, userId) => {
    try {
      const result = await testDatabaseConnectionById(serverId, databaseName, userId);
      return result;
    } catch (error) {
      console.error('测试数据库连接失败:', error);
      return {
        success: false,
        message: '测试数据库连接失败'
      };
    }
  });

  // 更新服务器状态
  ipcMain.handle('update-server-status', async (event, serverId, status) => {
    try {
      const result = await updateServerStatus(serverId, status);
      return result;
    } catch (error) {
      console.error('更新服务器状态失败:', error);
      return { success: false, message: '更新服务器状态失败' };
    }
  });

  // 执行备份
  ipcMain.handle('execute-backup', async (event, taskId) => {
    try {
      const result = await executeBackup(taskId);
      return result;
    } catch (error) {
      console.error('执行备份失败:', error);
      return { success: false, message: '执行备份失败' };
    }
  });

  // 获取备份文件路径（用于下载）
  ipcMain.handle('get-backup-file-path', async (event, historyId, userId) => {
    try {
      const result = await getBackupFilePath(historyId, userId);
      return result;
    } catch (error) {
      console.error('获取备份文件路径失败:', error);
      return { success: false, message: '获取备份文件路径失败' };
    }
  });

  // 在文件管理器中显示文件
  ipcMain.handle('show-item-in-folder', async (event, filePath) => {
    try {
      shell.showItemInFolder(filePath);
      return { success: true };
    } catch (error) {
      console.error('打开文件夹失败:', error);
      return { success: false, message: '打开文件夹失败' };
    }
  });

  // 获取系统信息
  ipcMain.handle('get-system-info', async (event, userId) => {
    try {
      // 获取系统基本信息
      const systemInfo = {
        productName: 'MySQL 增量备份系统',
        version: '1.0.0',
        edition: '企业版',
        licenseTo: '企业用户',
        installDate: new Date('2024-01-15'),
        lastUpdateCheck: new Date(),
        platform: os.platform(),
        arch: os.arch(),
        hostname: os.hostname(),
        uptime: os.uptime() * 1000, // 转换为毫秒
        startTime: new Date(Date.now() - os.uptime() * 1000)
      };

      return { success: true, data: systemInfo };
    } catch (error) {
      console.error('获取系统信息失败:', error);
      return { success: false, message: '获取系统信息失败' };
    }
  });

  // 获取系统指标
  ipcMain.handle('get-system-metrics', async (event) => {
    try {
      const totalMem = os.totalmem();
      const freeMem = os.freemem();
      const usedMem = totalMem - freeMem;

      // 获取CPU信息
      const cpus = os.cpus();
      const cpuCount = cpus.length;

      // 简单的CPU使用率计算（这里使用负载平均值作为近似）
      const loadAvg = os.loadavg();
      const cpuUsage = Math.min((loadAvg[0] / cpuCount) * 100, 100);

      const metrics = {
        timestamp: new Date(),
        cpu: {
          usage: cpuUsage,
          cores: cpuCount,
          loadAverage: loadAvg
        },
        memory: {
          total: totalMem,
          used: usedMem,
          free: freeMem,
          usage: (usedMem / totalMem) * 100
        },
        uptime: os.uptime() * 1000
      };

      return { success: true, data: metrics };
    } catch (error) {
      console.error('获取系统指标失败:', error);
      return { success: false, message: '获取系统指标失败' };
    }
  });

  // 获取备份统计信息
  ipcMain.handle('get-backup-stats', async (event, userId) => {
    try {
      // 获取用户的服务器数量
      const serversResult = await getUserServers(userId);
      const connectedDatabases = serversResult.success ? serversResult.servers.length : 0;

      // 获取用户的备份任务
      const tasksResult = await getUserBackupTasks(userId);
      const allTasks = tasksResult.success ? tasksResult.tasks : [];

      // 计算活跃任务（启用的任务）
      const activeBackupJobs = allTasks.filter(task => task.enabled).length;

      // 获取备份历史统计
      const historyResult = await getBackupHistory(userId);
      const backupHistory = historyResult.success ? historyResult.history : [];

      // 计算24小时内的备份统计
      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const recent24hBackups = backupHistory.filter(backup =>
        new Date(backup.created_at) >= yesterday
      );

      const completedBackups24h = recent24hBackups.filter(backup =>
        backup.status === 'completed'
      ).length;

      const failedBackups24h = recent24hBackups.filter(backup =>
        backup.status === 'failed'
      ).length;

      // 计算总备份大小
      const totalBackupSize = backupHistory
        .filter(backup => backup.status === 'completed' && backup.file_size)
        .reduce((total, backup) => total + (backup.file_size || 0), 0);

      // 计算平均备份时间
      const completedBackups = backupHistory.filter(backup =>
        backup.status === 'completed' && backup.duration
      );
      const averageBackupDuration = completedBackups.length > 0
        ? completedBackups.reduce((total, backup) => total + (backup.duration || 0), 0) / completedBackups.length
        : 0;

      // 获取最新备份时间
      const latestBackup = backupHistory.length > 0
        ? backupHistory.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0]
        : null;

      const stats = {
        timestamp: new Date(),
        connectedDatabases,
        activeBackupJobs,
        totalBackups: backupHistory.length,
        completedBackups24h,
        failedBackups24h,
        totalBackupSize,
        averageBackupDuration,
        latestBackup: latestBackup ? {
          time: latestBackup.created_at,
          status: latestBackup.status,
          serverName: latestBackup.server_name,
          databaseName: latestBackup.database_name
        } : null
      };

      return { success: true, data: stats };
    } catch (error) {
      console.error('获取备份统计失败:', error);
      return { success: false, message: '获取备份统计失败' };
    }
  });

  // 获取服务器端备份文件列表
  ipcMain.handle('get-server-backup-files', async (event, serverId, userId) => {
    try {
      console.log('Getting server backup files:', { serverId, userId });
      const result = await getServerBackupFiles(serverId, userId);
      console.log('Server backup files result:', result);
      return result;
    } catch (error: any) {
      console.error('Get server backup files failed:', error);
      return {
        success: false,
        message: `获取服务器备份文件失败: ${error?.message || error?.toString() || '未知错误'}`
      };
    }
  });

  // 下载服务器端备份文件
  ipcMain.handle('download-server-backup-file', async (event, serverId, filePath, userId) => {
    try {
      console.log('Downloading server backup file:', { serverId, filePath, userId });
      const result = await downloadServerBackupFile(serverId, filePath, userId);
      console.log('Download server backup file result:', result.success);
      return result;
    } catch (error: any) {
      console.error('Download server backup file failed:', error);
      return {
        success: false,
        message: `下载服务器备份文件失败: ${error?.message || error?.toString() || '未知错误'}`
      };
    }
  });

  // 恢复备份到指定时间点
  ipcMain.handle('restore-backup-to-point-in-time', async (event, taskId, targetHistoryId, userId, options) => {
    try {
      console.log('Restoring backup to point in time:', { taskId, targetHistoryId, userId, options });
      const result = await restoreBackupToPointInTime(taskId, targetHistoryId, userId, options);
      console.log('Restore backup result:', result.success);
      return result;
    } catch (error: any) {
      console.error('Restore backup failed:', error);
      return {
        success: false,
        message: `恢复备份失败: ${error?.message || error?.toString() || '未知错误'}`
      };
    }
  });

  // 验证备份文件完整性
  ipcMain.handle('validate-backup-file-integrity', async (event, historyId, userId) => {
    try {
      console.log('Validating backup file integrity:', { historyId, userId });
      const result = await validateBackupFileIntegrity(historyId, userId);
      console.log('Validate backup file integrity result:', result.success);
      return result;
    } catch (error: any) {
      console.error('Validate backup file integrity failed:', error);
      return {
        success: false,
        message: `验证备份文件完整性失败: ${error?.message || error?.toString() || '未知错误'}`
      };
    }
  });

  // 修复备份链
  ipcMain.handle('repair-backup-chain', async (event, taskId, userId, options) => {
    try {
      console.log('Repairing backup chain:', { taskId, userId, options });
      const result = await repairBackupChain(taskId, userId, options);
      console.log('Repair backup chain result:', result.success);
      return result;
    } catch (error: any) {
      console.error('Repair backup chain failed:', error);
      return {
        success: false,
        message: `修复备份链失败: ${error?.message || error?.toString() || '未知错误'}`
      };
    }
  });
}

app.whenReady().then(initializeApp)
