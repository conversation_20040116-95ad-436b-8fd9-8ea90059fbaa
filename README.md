# MySQL增量备份桌面应用

这是一个基于Electron + React + TypeScript开发的MySQL增量备份桌面应用，支持用户注册登录和MySQL数据库的增量备份功能。

## 功能特性

### 已完成功能
- ✅ 用户注册和登录系统
- ✅ 邮箱格式验证
- ✅ 密码加密存储
- ✅ JWT token认证
- ✅ 响应式用户界面
- ✅ 数据库连接管理

### 计划功能
- 🔄 MySQL服务器连接配置
- 🔄 增量备份任务创建和管理
- 🔄 SSH连接到远程服务器
- 🔄 备份文件管理
- 🔄 备份历史记录
- 🔄 定时备份任务
- 🔄 备份恢复功能

## 技术栈

- **前端**: React 18 + TypeScript + CSS3
- **桌面框架**: Electron
- **数据库**: MySQL 8.0
- **认证**: JWT + bcryptjs
- **构建工具**: Vite
- **包管理**: npm

## 安装和运行

### 环境要求
- Node.js 16+
- npm 或 yarn
- MySQL 8.0+

### 安装依赖
```bash
npm install
```

### 开发模式运行
```bash
npm run dev
```

### 构建应用
```bash
npm run build
```

## 数据库配置

应用使用以下数据库配置（在 `electron/database.ts` 中）：
- 主机: ***************
- 数据库: user
- 用户名: user-hiram
- 密码: user-hiram

用户表结构：
```sql
CREATE TABLE users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 使用说明

### 1. 用户注册
- 填写用户名（至少3个字符）
- 填写有效的邮箱地址
- 设置密码（至少6个字符）
- 确认密码

### 2. 用户登录
- 输入用户名和密码
- 系统会验证凭据并生成JWT token

### 3. 主界面功能
登录成功后，可以访问以下功能模块：
- **备份管理**: 创建和管理备份任务
- **服务器管理**: 配置MySQL服务器连接
- **备份历史**: 查看备份记录
- **设置**: 用户信息和应用配置

## 项目结构

```
├── electron/                 # Electron主进程代码
│   ├── main.ts              # 主进程入口
│   ├── preload.ts           # 预加载脚本
│   └── database.ts          # 数据库操作模块
├── src/                     # React渲染进程代码
│   ├── components/          # React组件
│   │   ├── Login.tsx        # 登录组件
│   │   ├── Register.tsx     # 注册组件
│   │   ├── Dashboard.tsx    # 主界面组件
│   │   ├── Auth.css         # 认证组件样式
│   │   └── Dashboard.css    # 主界面样式
│   ├── types/               # TypeScript类型定义
│   │   └── electron.d.ts    # Electron API类型
│   ├── App.tsx              # 应用根组件
│   ├── App.css              # 应用样式
│   ├── index.css            # 全局样式
│   └── main.tsx             # React入口
├── package.json             # 项目配置
└── README.md               # 项目说明
```

## 开发说明

### IPC通信
应用使用Electron的IPC机制进行主进程和渲染进程之间的通信：

- `user-register`: 用户注册
- `user-login`: 用户登录

### 安全性
- 密码使用bcryptjs进行哈希加密
- JWT token用于用户会话管理
- 数据库连接使用连接池
- 输入验证和错误处理

### 样式设计
- 响应式设计，支持不同屏幕尺寸
- 现代化的UI设计
- 渐变色彩和动画效果
- 自定义滚动条样式

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
