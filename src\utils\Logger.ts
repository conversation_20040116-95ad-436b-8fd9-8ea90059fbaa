/**
 * 企业级日志系统
 * 支持结构化日志、日志轮转、敏感数据脱敏、审计日志
 */

import * as fs from 'fs';
import * as path from 'path';
import { config } from '../config/AppConfig';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

export interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  module?: string;
  userId?: number;
  sessionId?: string;
  requestId?: string;
  metadata?: any;
  stack?: string;
}

export interface AuditLogEntry extends LogEntry {
  action: string;
  resource: string;
  resourceId?: string;
  oldValue?: any;
  newValue?: any;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * 企业级日志记录器
 */
export class Logger {
  private static instance: Logger;
  private logLevel: LogLevel;
  private logDir: string;
  private auditLogDir: string;
  private sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];

  private constructor() {
    const logConfig = config.get<any>('logging');
    this.logLevel = this.getLogLevel(logConfig.level);
    this.logDir = path.join(process.cwd(), 'logs');
    this.auditLogDir = path.join(this.logDir, 'audit');
    this.ensureLogDirectories();
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private getLogLevel(level: string): LogLevel {
    switch (level.toLowerCase()) {
      case 'debug': return LogLevel.DEBUG;
      case 'info': return LogLevel.INFO;
      case 'warn': return LogLevel.WARN;
      case 'error': return LogLevel.ERROR;
      default: return LogLevel.INFO;
    }
  }

  private ensureLogDirectories(): void {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
    if (!fs.existsSync(this.auditLogDir)) {
      fs.mkdirSync(this.auditLogDir, { recursive: true });
    }
  }

  private maskSensitiveData(data: any): any {
    if (!config.get<boolean>('logging.sensitiveDataMasking')) {
      return data;
    }

    if (typeof data === 'string') {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.maskSensitiveData(item));
    }

    if (typeof data === 'object' && data !== null) {
      const masked = { ...data };
      for (const key in masked) {
        if (this.sensitiveFields.some(field => key.toLowerCase().includes(field))) {
          masked[key] = '***MASKED***';
        } else if (typeof masked[key] === 'object') {
          masked[key] = this.maskSensitiveData(masked[key]);
        }
      }
      return masked;
    }

    return data;
  }

  private formatLogEntry(entry: LogEntry): string {
    const maskedEntry = this.maskSensitiveData(entry);
    return JSON.stringify(maskedEntry) + '\n';
  }

  private writeToFile(filename: string, content: string): void {
    const filePath = path.join(this.logDir, filename);
    try {
      fs.appendFileSync(filePath, content);
      this.rotateLogIfNeeded(filePath);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  private rotateLogIfNeeded(filePath: string): void {
    try {
      const stats = fs.statSync(filePath);
      const maxSize = config.get<number>('logging.maxFileSize') * 1024 * 1024; // Convert MB to bytes
      
      if (stats.size > maxSize) {
        const maxFiles = config.get<number>('logging.maxFiles');
        const dir = path.dirname(filePath);
        const ext = path.extname(filePath);
        const basename = path.basename(filePath, ext);
        
        // Rotate existing files
        for (let i = maxFiles - 1; i > 0; i--) {
          const oldFile = path.join(dir, `${basename}.${i}${ext}`);
          const newFile = path.join(dir, `${basename}.${i + 1}${ext}`);
          if (fs.existsSync(oldFile)) {
            if (i === maxFiles - 1) {
              fs.unlinkSync(oldFile); // Delete oldest file
            } else {
              fs.renameSync(oldFile, newFile);
            }
          }
        }
        
        // Move current file to .1
        const rotatedFile = path.join(dir, `${basename}.1${ext}`);
        fs.renameSync(filePath, rotatedFile);
      }
    } catch (error) {
      console.error('Failed to rotate log file:', error);
    }
  }

  private log(level: LogLevel, message: string, metadata?: any, module?: string): void {
    if (level < this.logLevel) {
      return;
    }

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: LogLevel[level],
      message,
      module,
      metadata: metadata ? this.maskSensitiveData(metadata) : undefined
    };

    const logContent = this.formatLogEntry(entry);
    
    // Write to console in development
    if (config.get<string>('environment') === 'development') {
      console.log(logContent.trim());
    }

    // Write to file
    const today = new Date().toISOString().split('T')[0];
    this.writeToFile(`app-${today}.log`, logContent);

    // Write errors to separate file
    if (level === LogLevel.ERROR) {
      this.writeToFile(`error-${today}.log`, logContent);
    }
  }

  public debug(message: string, metadata?: any, module?: string): void {
    this.log(LogLevel.DEBUG, message, metadata, module);
  }

  public info(message: string, metadata?: any, module?: string): void {
    this.log(LogLevel.INFO, message, metadata, module);
  }

  public warn(message: string, metadata?: any, module?: string): void {
    this.log(LogLevel.WARN, message, metadata, module);
  }

  public error(message: string, error?: Error, metadata?: any, module?: string): void {
    const logMetadata = {
      ...metadata,
      stack: error?.stack,
      errorMessage: error?.message
    };
    this.log(LogLevel.ERROR, message, logMetadata, module);
  }

  /**
   * 审计日志记录
   */
  public audit(entry: Omit<AuditLogEntry, 'timestamp' | 'level'>): void {
    if (!config.get<boolean>('logging.auditEnabled')) {
      return;
    }

    const auditEntry: AuditLogEntry = {
      ...entry,
      timestamp: new Date().toISOString(),
      level: 'AUDIT'
    };

    const logContent = this.formatLogEntry(auditEntry);
    const today = new Date().toISOString().split('T')[0];
    const auditFilePath = path.join(this.auditLogDir, `audit-${today}.log`);
    
    try {
      fs.appendFileSync(auditFilePath, logContent);
    } catch (error) {
      this.error('Failed to write audit log', error as Error, { auditEntry });
    }
  }

  /**
   * 性能日志记录
   */
  public performance(operation: string, duration: number, metadata?: any): void {
    this.info(`Performance: ${operation}`, {
      duration,
      operation,
      ...metadata
    }, 'PERFORMANCE');
  }

  /**
   * 安全事件日志记录
   */
  public security(event: string, severity: 'low' | 'medium' | 'high' | 'critical', metadata?: any): void {
    this.warn(`Security Event: ${event}`, {
      severity,
      event,
      ...metadata
    }, 'SECURITY');
  }

  /**
   * 清理过期日志文件
   */
  public cleanupOldLogs(): void {
    const retentionDays = config.get<number>('logging.logRetentionDays');
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const cleanupDirectory = (dir: string) => {
      try {
        const files = fs.readdirSync(dir);
        for (const file of files) {
          const filePath = path.join(dir, file);
          const stats = fs.statSync(filePath);
          if (stats.mtime < cutoffDate) {
            fs.unlinkSync(filePath);
            this.info(`Cleaned up old log file: ${file}`);
          }
        }
      } catch (error) {
        this.error('Failed to cleanup old logs', error as Error, { directory: dir });
      }
    };

    cleanupDirectory(this.logDir);
    cleanupDirectory(this.auditLogDir);
  }
}

// 导出单例实例
export const logger = Logger.getInstance();

// 导出便捷函数
export const log = {
  debug: (message: string, metadata?: any, module?: string) => logger.debug(message, metadata, module),
  info: (message: string, metadata?: any, module?: string) => logger.info(message, metadata, module),
  warn: (message: string, metadata?: any, module?: string) => logger.warn(message, metadata, module),
  error: (message: string, error?: Error, metadata?: any, module?: string) => logger.error(message, error, metadata, module),
  audit: (entry: Omit<AuditLogEntry, 'timestamp' | 'level'>) => logger.audit(entry),
  performance: (operation: string, duration: number, metadata?: any) => logger.performance(operation, duration, metadata),
  security: (event: string, severity: 'low' | 'medium' | 'high' | 'critical', metadata?: any) => logger.security(event, severity, metadata)
};
