import React, { useState, useEffect } from 'react';
import './ServerBackupFiles.css';

interface BackupFile {
  path: string;
  name: string;
  size: number;
  modifiedTime: Date;
  backupPath: string;
}

interface ServerBackupFilesProps {
  userId: number;
  serverId: number;
  serverName: string;
  onClose: () => void;
}

const ServerBackupFiles: React.FC<ServerBackupFilesProps> = ({ 
  userId, 
  serverId, 
  serverName, 
  onClose 
}) => {
  const [files, setFiles] = useState<BackupFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [downloading, setDownloading] = useState<string | null>(null);

  // 加载服务器备份文件
  const loadServerFiles = async () => {
    try {
      setLoading(true);
      setError('');
      
      console.log('Loading server backup files for:', { serverId, userId });
      const result = await window.electronAPI.getServerBackupFiles(serverId, userId);
      
      if (result.success) {
        setFiles(result.files || []);
      } else {
        setError(result.message || '获取服务器备份文件失败');
      }
    } catch (error: any) {
      console.error('Load server files error:', error);
      setError(`加载服务器文件失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 下载备份文件
  const downloadFile = async (filePath: string, fileName: string) => {
    try {
      setDownloading(filePath);
      
      console.log('Downloading file:', { filePath, fileName });
      const result = await window.electronAPI.downloadServerBackupFile(serverId, filePath, userId);
      
      if (result.success && result.content) {
        // 创建下载链接
        const blob = new Blob([result.content], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = result.fileName || fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } else {
        setError(result.message || '下载文件失败');
      }
    } catch (error: any) {
      console.error('Download file error:', error);
      setError(`下载文件失败: ${error.message}`);
    } finally {
      setDownloading(null);
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化时间
  const formatTime = (date: Date): string => {
    return new Date(date).toLocaleString('zh-CN');
  };

  useEffect(() => {
    loadServerFiles();
  }, [serverId, userId]);

  return (
    <div className="server-backup-files-overlay">
      <div className="server-backup-files-modal">
        <div className="server-backup-files-header">
          <h2>服务器备份文件 - {serverName}</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="server-backup-files-content">
          {loading && (
            <div className="loading-state">
              <div className="loading-spinner"></div>
              <p>正在加载服务器备份文件...</p>
            </div>
          )}

          {error && (
            <div className="error-state">
              <p className="error-message">{error}</p>
              <button onClick={loadServerFiles} className="retry-button">
                重试
              </button>
            </div>
          )}

          {!loading && !error && files.length === 0 && (
            <div className="empty-state">
              <p>该服务器暂无备份文件</p>
            </div>
          )}

          {!loading && !error && files.length > 0 && (
            <div className="files-list">
              <div className="files-header">
                <span>文件名</span>
                <span>大小</span>
                <span>修改时间</span>
                <span>备份路径</span>
                <span>操作</span>
              </div>

              {files.map((file, index) => (
                <div key={index} className="file-item">
                  <span className="file-name" title={file.name}>
                    {file.name}
                  </span>
                  <span className="file-size">
                    {formatFileSize(file.size)}
                  </span>
                  <span className="file-time">
                    {formatTime(file.modifiedTime)}
                  </span>
                  <span className="file-path" title={file.backupPath}>
                    {file.backupPath}
                  </span>
                  <span className="file-actions">
                    <button
                      onClick={() => downloadFile(file.path, file.name)}
                      disabled={downloading === file.path}
                      className="download-button"
                    >
                      {downloading === file.path ? '下载中...' : '下载'}
                    </button>
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="server-backup-files-footer">
          <button onClick={loadServerFiles} className="refresh-button">
            刷新列表
          </button>
          <button onClick={onClose} className="close-footer-button">
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

export default ServerBackupFiles;
