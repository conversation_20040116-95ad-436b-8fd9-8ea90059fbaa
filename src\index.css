* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* 桌面应用主题色彩 */
  --primary-color: #0078d4;
  --primary-hover: #106ebe;
  --primary-active: #005a9e;
  --secondary-color: #6c757d;
  --success-color: #107c10;
  --warning-color: #ff8c00;
  --danger-color: #d13438;
  --error-color: #dc2626;
  --info-color: #0ea5e9;

  /* 背景色 */
  --bg-primary: #f3f2f1;
  --bg-secondary: #ffffff;
  --bg-tertiary: #faf9f8;
  --bg-hover: #f8f8f8;
  --bg-active: #edebe9;

  /* 文字颜色 */
  --text-primary: #323130;
  --text-secondary: #605e5c;
  --text-tertiary: #8a8886;
  --text-disabled: #a19f9d;

  /* 边框颜色 */
  --border-color: #e1dfdd;
  --border-hover: #c8c6c4;
  --border-focus: #0078d4;

  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.04);
  --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.16);

  /* 字体 */
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow-x: hidden;
  background: var(--bg-primary);
}

#root {
  width: 100%;
  height: 100vh;
  background: var(--bg-primary);
}

/* 链接样式 */
a {
  font-weight: 500;
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* 按钮基础样式 */
button {
  border-radius: 4px;
  border: 1px solid transparent;
  font-family: inherit;
  font-size: 14px;
  font-weight: 600;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  min-height: 32px;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 主要按钮 */
.primary-button {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.primary-button:hover:not(:disabled) {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.primary-button:active:not(:disabled) {
  background: var(--primary-active);
  border-color: var(--primary-active);
}

/* 次要按钮 */
.secondary-button {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.secondary-button:hover:not(:disabled) {
  background: var(--bg-hover);
  border-color: var(--border-hover);
}

.secondary-button:active:not(:disabled) {
  background: var(--bg-active);
}

/* 危险按钮 */
.danger-button {
  background: var(--danger-color);
  color: white;
  border-color: var(--danger-color);
}

.danger-button:hover:not(:disabled) {
  background: #b02e31;
  border-color: #b02e31;
}

/* 成功按钮 */
.success-button {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.success-button:hover:not(:disabled) {
  background: #0e6e0e;
  border-color: #0e6e0e;
}

/* 输入框样式 */
input, textarea, select {
  font-family: inherit;
  font-size: 14px;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  outline: none;
  transition: all 0.2s ease;
}

input:focus, textarea:focus, select:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 1px var(--border-focus);
}

input:disabled, textarea:disabled, select:disabled {
  background: var(--bg-tertiary);
  color: var(--text-disabled);
  cursor: not-allowed;
}

/* 滚动条样式 - 现代化 */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-hover);
  border-radius: 6px;
  border: 2px solid var(--bg-tertiary);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

::-webkit-scrollbar-corner {
  background: var(--bg-tertiary);
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--bg-secondary);
  border-radius: 8px;
  padding: 24px;
  min-width: 400px;
  max-width: 500px;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-color);
}

.modal-content h4 {
  margin-bottom: 16px;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
}

.modal-content p {
  margin-bottom: 20px;
  color: var(--text-secondary);
  font-size: 14px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}

/* 备份历史样式 */
.backup-history {
  padding: var(--spacing-lg, 24px);
}

.col-actions {
  min-width: 100px;
  text-align: center;
}

.download-button {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  min-height: 28px;
}

.download-button:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

.download-button:disabled {
  background: var(--border-color);
  cursor: not-allowed;
  transform: none;
}
