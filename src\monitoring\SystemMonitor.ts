/**
 * 企业级系统监控和告警系统
 * 监控系统性能、备份状态、资源使用情况
 */

import * as os from 'os';
import * as fs from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';
import { logger } from '../utils/Logger';
import { config } from '../config/AppConfig';

export interface SystemMetrics {
  timestamp: Date;
  cpu: {
    usage: number; // 百分比
    loadAverage: number[];
  };
  memory: {
    total: number; // bytes
    used: number; // bytes
    free: number; // bytes
    usage: number; // 百分比
  };
  disk: {
    total: number; // bytes
    used: number; // bytes
    free: number; // bytes
    usage: number; // 百分比
  };
  network: {
    bytesReceived: number;
    bytesSent: number;
  };
  processes: {
    total: number;
    running: number;
  };
}

export interface BackupMetrics {
  timestamp: Date;
  activeBackups: number;
  queuedBackups: number;
  completedBackups24h: number;
  failedBackups24h: number;
  averageBackupDuration: number; // minutes
  totalBackupSize: number; // bytes
  oldestBackup: Date;
  newestBackup: Date;
}

export interface AlertRule {
  id: string;
  name: string;
  description: string;
  metric: string;
  operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  cooldownMinutes: number;
  lastTriggered?: Date;
}

export interface Alert {
  id: string;
  ruleId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  timestamp: Date;
  acknowledged: boolean;
  acknowledgedBy?: number;
  acknowledgedAt?: Date;
  resolved: boolean;
  resolvedAt?: Date;
  metadata?: any;
}

/**
 * 系统监控器
 */
export class SystemMonitor extends EventEmitter {
  private static instance: SystemMonitor;
  private isRunning = false;
  private metricsInterval?: NodeJS.Timeout;
  private alertRules: Map<string, AlertRule> = new Map();
  private activeAlerts: Map<string, Alert> = new Map();
  private metricsHistory: SystemMetrics[] = [];
  private backupMetricsHistory: BackupMetrics[] = [];
  private maxHistorySize = 1440; // 24小时的分钟数

  private constructor() {
    super();
    this.initializeDefaultAlertRules();
  }

  public static getInstance(): SystemMonitor {
    if (!SystemMonitor.instance) {
      SystemMonitor.instance = new SystemMonitor();
    }
    return SystemMonitor.instance;
  }

  /**
   * 启动监控
   */
  public start(): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    const interval = config.get<number>('monitoring.metricsInterval') * 1000;

    this.metricsInterval = setInterval(() => {
      this.collectMetrics();
    }, interval);

    logger.info('系统监控已启动', { interval: interval / 1000 });
  }

  /**
   * 停止监控
   */
  public stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = undefined;
    }

    logger.info('系统监控已停止');
  }

  /**
   * 收集系统指标
   */
  private async collectMetrics(): Promise<void> {
    try {
      const systemMetrics = await this.getSystemMetrics();
      const backupMetrics = await this.getBackupMetrics();

      // 保存指标历史
      this.metricsHistory.push(systemMetrics);
      this.backupMetricsHistory.push(backupMetrics);

      // 限制历史记录大小
      if (this.metricsHistory.length > this.maxHistorySize) {
        this.metricsHistory.shift();
      }
      if (this.backupMetricsHistory.length > this.maxHistorySize) {
        this.backupMetricsHistory.shift();
      }

      // 检查告警规则
      this.checkAlertRules(systemMetrics, backupMetrics);

      // 发出指标更新事件
      this.emit('metricsUpdated', { systemMetrics, backupMetrics });

    } catch (error) {
      logger.error('收集系统指标失败', error as Error);
    }
  }

  /**
   * 获取系统指标
   */
  private async getSystemMetrics(): Promise<SystemMetrics> {
    const cpuUsage = await this.getCpuUsage();
    const memoryInfo = this.getMemoryInfo();
    const diskInfo = await this.getDiskInfo();
    const networkInfo = this.getNetworkInfo();

    return {
      timestamp: new Date(),
      cpu: {
        usage: cpuUsage,
        loadAverage: os.loadavg()
      },
      memory: memoryInfo,
      disk: diskInfo,
      network: networkInfo,
      processes: {
        total: 0, // 需要实现
        running: 0 // 需要实现
      }
    };
  }

  /**
   * 获取CPU使用率
   */
  private async getCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startMeasure = this.cpuAverage();
      
      setTimeout(() => {
        const endMeasure = this.cpuAverage();
        const idleDifference = endMeasure.idle - startMeasure.idle;
        const totalDifference = endMeasure.total - startMeasure.total;
        const usage = 100 - ~~(100 * idleDifference / totalDifference);
        resolve(usage);
      }, 1000);
    });
  }

  private cpuAverage() {
    const cpus = os.cpus();
    let user = 0, nice = 0, sys = 0, idle = 0, irq = 0;
    
    for (const cpu of cpus) {
      user += cpu.times.user;
      nice += cpu.times.nice;
      sys += cpu.times.sys;
      idle += cpu.times.idle;
      irq += cpu.times.irq;
    }
    
    const total = user + nice + sys + idle + irq;
    return { idle, total };
  }

  /**
   * 获取内存信息
   */
  private getMemoryInfo() {
    const total = os.totalmem();
    const free = os.freemem();
    const used = total - free;
    const usage = (used / total) * 100;

    return {
      total,
      used,
      free,
      usage
    };
  }

  /**
   * 获取磁盘信息
   */
  private async getDiskInfo() {
    try {
      const stats = fs.statSync(process.cwd());
      // 这里需要使用更准确的磁盘空间检测方法
      // 简化实现，实际应该使用 statvfs 或类似的系统调用
      return {
        total: 1000000000000, // 1TB 示例
        used: 500000000000,   // 500GB 示例
        free: 500000000000,   // 500GB 示例
        usage: 50             // 50% 示例
      };
    } catch (error) {
      return {
        total: 0,
        used: 0,
        free: 0,
        usage: 0
      };
    }
  }

  /**
   * 获取网络信息
   */
  private getNetworkInfo() {
    const networkInterfaces = os.networkInterfaces();
    // 简化实现，实际应该读取 /proc/net/dev 或使用系统API
    return {
      bytesReceived: 0,
      bytesSent: 0
    };
  }

  /**
   * 获取备份指标
   */
  private async getBackupMetrics(): Promise<BackupMetrics> {
    // 这里需要从数据库或备份系统获取实际数据
    // 简化实现
    return {
      timestamp: new Date(),
      activeBackups: 0,
      queuedBackups: 0,
      completedBackups24h: 10,
      failedBackups24h: 1,
      averageBackupDuration: 15,
      totalBackupSize: 1000000000, // 1GB
      oldestBackup: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天前
      newestBackup: new Date()
    };
  }

  /**
   * 初始化默认告警规则
   */
  private initializeDefaultAlertRules(): void {
    const defaultRules: AlertRule[] = [
      {
        id: 'cpu_high',
        name: 'CPU使用率过高',
        description: 'CPU使用率超过阈值',
        metric: 'cpu.usage',
        operator: 'gt',
        threshold: config.get<number>('monitoring.performanceThreshold.cpuUsage'),
        severity: 'high',
        enabled: true,
        cooldownMinutes: 5
      },
      {
        id: 'memory_high',
        name: '内存使用率过高',
        description: '内存使用率超过阈值',
        metric: 'memory.usage',
        operator: 'gt',
        threshold: config.get<number>('monitoring.performanceThreshold.memoryUsage'),
        severity: 'high',
        enabled: true,
        cooldownMinutes: 5
      },
      {
        id: 'disk_high',
        name: '磁盘使用率过高',
        description: '磁盘使用率超过阈值',
        metric: 'disk.usage',
        operator: 'gt',
        threshold: config.get<number>('monitoring.performanceThreshold.diskUsage'),
        severity: 'critical',
        enabled: true,
        cooldownMinutes: 10
      },
      {
        id: 'backup_failed',
        name: '备份失败过多',
        description: '24小时内备份失败次数过多',
        metric: 'backup.failedBackups24h',
        operator: 'gt',
        threshold: 3,
        severity: 'high',
        enabled: true,
        cooldownMinutes: 60
      }
    ];

    defaultRules.forEach(rule => {
      this.alertRules.set(rule.id, rule);
    });
  }

  /**
   * 检查告警规则
   */
  private checkAlertRules(systemMetrics: SystemMetrics, backupMetrics: BackupMetrics): void {
    for (const rule of this.alertRules.values()) {
      if (!rule.enabled) {
        continue;
      }

      // 检查冷却时间
      if (rule.lastTriggered) {
        const cooldownMs = rule.cooldownMinutes * 60 * 1000;
        if (Date.now() - rule.lastTriggered.getTime() < cooldownMs) {
          continue;
        }
      }

      const value = this.getMetricValue(rule.metric, systemMetrics, backupMetrics);
      if (value !== null && this.evaluateCondition(value, rule.operator, rule.threshold)) {
        this.triggerAlert(rule, value);
      }
    }
  }

  /**
   * 获取指标值
   */
  private getMetricValue(metric: string, systemMetrics: SystemMetrics, backupMetrics: BackupMetrics): number | null {
    const parts = metric.split('.');
    let value: any = metric.startsWith('backup.') ? backupMetrics : systemMetrics;
    
    for (const part of parts) {
      value = value?.[part];
    }
    
    return typeof value === 'number' ? value : null;
  }

  /**
   * 评估条件
   */
  private evaluateCondition(value: number, operator: string, threshold: number): boolean {
    switch (operator) {
      case 'gt': return value > threshold;
      case 'lt': return value < threshold;
      case 'eq': return value === threshold;
      case 'gte': return value >= threshold;
      case 'lte': return value <= threshold;
      default: return false;
    }
  }

  /**
   * 触发告警
   */
  private triggerAlert(rule: AlertRule, value: number): void {
    const alert: Alert = {
      id: this.generateAlertId(),
      ruleId: rule.id,
      severity: rule.severity,
      title: rule.name,
      message: `${rule.description}: 当前值 ${value}, 阈值 ${rule.threshold}`,
      timestamp: new Date(),
      acknowledged: false,
      resolved: false,
      metadata: { value, threshold: rule.threshold }
    };

    this.activeAlerts.set(alert.id, alert);
    rule.lastTriggered = new Date();

    logger.warn('告警触发', { alert, rule });
    this.emit('alertTriggered', alert);

    // 发送通知
    this.sendNotification(alert);
  }

  /**
   * 发送通知
   */
  private async sendNotification(alert: Alert): Promise<void> {
    try {
      // 实现邮件、webhook等通知方式
      logger.info('发送告警通知', { alertId: alert.id, severity: alert.severity });
    } catch (error) {
      logger.error('发送告警通知失败', error as Error, { alertId: alert.id });
    }
  }

  /**
   * 获取当前指标
   */
  public getCurrentMetrics(): { system?: SystemMetrics; backup?: BackupMetrics } {
    return {
      system: this.metricsHistory[this.metricsHistory.length - 1],
      backup: this.backupMetricsHistory[this.backupMetricsHistory.length - 1]
    };
  }

  /**
   * 获取指标历史
   */
  public getMetricsHistory(hours: number = 24): { system: SystemMetrics[]; backup: BackupMetrics[] } {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    
    return {
      system: this.metricsHistory.filter(m => m.timestamp >= cutoff),
      backup: this.backupMetricsHistory.filter(m => m.timestamp >= cutoff)
    };
  }

  /**
   * 获取活跃告警
   */
  public getActiveAlerts(): Alert[] {
    return Array.from(this.activeAlerts.values()).filter(alert => !alert.resolved);
  }

  /**
   * 确认告警
   */
  public acknowledgeAlert(alertId: string, userId: number): boolean {
    const alert = this.activeAlerts.get(alertId);
    if (!alert) {
      return false;
    }

    alert.acknowledged = true;
    alert.acknowledgedBy = userId;
    alert.acknowledgedAt = new Date();

    logger.info('告警已确认', { alertId, userId });
    return true;
  }

  /**
   * 解决告警
   */
  public resolveAlert(alertId: string): boolean {
    const alert = this.activeAlerts.get(alertId);
    if (!alert) {
      return false;
    }

    alert.resolved = true;
    alert.resolvedAt = new Date();

    logger.info('告警已解决', { alertId });
    return true;
  }

  private generateAlertId(): string {
    return 'alert_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}

export const systemMonitor = SystemMonitor.getInstance();
