.dashboard {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--bg-primary);
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  padding: 20px 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 80px;
}

.header-left {
  flex: 1;
}

.app-branding {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.app-title {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.app-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.welcome-text {
  font-size: 14px;
  color: white;
  font-weight: 500;
}

.user-role {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.logout-button {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.logout-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.dashboard-nav {
  display: flex;
  background: white;
  border-bottom: 1px solid var(--border-color);
  padding: 0 32px;
  overflow-x: auto;
  min-height: 48px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.nav-button {
  background: none;
  border: none;
  padding: 12px 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
  white-space: nowrap;
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-button:hover {
  color: var(--primary-color);
  background: rgba(102, 126, 234, 0.05);
}

.nav-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background: rgba(102, 126, 234, 0.1);
  font-weight: 600;
}

.nav-button::before {
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: transparent;
  transition: all 0.2s ease;
}

.nav-button.active::before {
  background: var(--primary-color);
}

.dashboard-content {
  flex: 1;
  padding: 20px 24px;
  overflow-y: auto;
  background: var(--bg-primary);
}

.content-panel {
  max-width: 1200px;
  margin: 0 auto;
}

.content-panel h2 {
  color: var(--text-primary);
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: 600;
}

.backup-section,
.servers-section,
.history-section,
.settings-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-card {
  background: var(--bg-secondary);
  border-radius: 8px;
  padding: 20px;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
}

.info-card h3 {
  color: var(--text-primary);
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.info-card p {
  color: var(--text-secondary);
  margin: 0 0 16px 0;
  line-height: 1.5;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 按钮样式已在 index.css 中定义，这里只需要特定的覆盖 */

.user-details {
  background: var(--bg-tertiary);
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
  border: 1px solid var(--border-color);
}

.user-details p {
  margin: 6px 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.user-details strong {
  color: var(--text-primary);
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .header-left h1 {
    font-size: 20px;
  }

  .dashboard-nav {
    padding: 0 20px;
  }

  .nav-button {
    padding: 12px 16px;
    font-size: 13px;
  }

  .dashboard-content {
    padding: 20px;
  }

  .content-panel h2 {
    font-size: 24px;
  }

  .info-card {
    padding: 20px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .primary-button,
  .secondary-button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 12px 15px;
  }

  .dashboard-nav {
    padding: 0 15px;
  }

  .dashboard-content {
    padding: 15px;
  }

  .info-card {
    padding: 15px;
  }
}

/* 服务器管理样式 */
.server-management {
  padding: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  color: #333;
}

.add-server-form {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.add-server-form h4 {
  margin: 0 0 20px 0;
  color: #333;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.ssh-section {
  border-top: 1px solid #e9ecef;
  padding-top: 20px;
  margin-top: 20px;
}

.ssh-section h5 {
  margin: 0 0 15px 0;
  color: #666;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.servers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.server-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.server-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.server-header h4 {
  margin: 0;
  color: #333;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.error {
  background: #f8d7da;
  color: #721c24;
}

.server-details p {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
}

.server-actions {
  margin-top: 15px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.server-actions button {
  padding: 6px 12px;
  font-size: 12px;
  min-width: 70px;
}

/* 输入框与按钮组合 */
.input-with-button {
  display: flex;
  gap: 8px;
  align-items: center;
}

.input-with-button input {
  flex: 1;
}

.input-with-button button {
  padding: 8px 12px;
  font-size: 12px;
  white-space: nowrap;
}

/* 备份任务管理样式 */
.backup-management {
  padding: 20px;
}

.add-task-form {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.tasks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.task-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.task-header h4 {
  margin: 0;
  color: #333;
}

.status-badge.running {
  background: #fff3cd;
  color: #856404;
}

.task-details p {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
}

.task-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

/* 备份历史样式 */
.backup-history {
  padding: 20px;
}

.controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.limit-select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.history-table {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1.5fr 0.8fr 0.8fr 0.8fr 1fr 1.5fr;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 15px;
  font-weight: 600;
  color: #333;
}

.table-body {
  max-height: 600px;
  overflow-y: auto;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1.5fr 0.8fr 0.8fr 0.8fr 1fr 1.5fr;
  padding: 15px;
  border-bottom: 1px solid #f1f3f4;
  align-items: center;
}

.table-row:hover {
  background: #f8f9fa;
}

.task-info,
.server-info,
.time-info {
  display: flex;
  flex-direction: column;
}

.task-name,
.server-name,
.start-time {
  font-weight: 500;
  color: #333;
}

.file-path,
.server-host,
.end-time {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.backup-type {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.backup-type.full {
  background: #e3f2fd;
  color: #1565c0;
}

.backup-type.incremental {
  background: #f3e5f5;
  color: #7b1fa2;
}

.status-badge.status-running {
  background: #fff3cd;
  color: #856404;
}

.status-badge.status-completed {
  background: #d4edda;
  color: #155724;
}

.status-badge.status-failed {
  background: #f8d7da;
  color: #721c24;
}

.error-tooltip {
  display: inline-block;
  margin-left: 5px;
  cursor: help;
}

.history-stats {
  margin-top: 20px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.stat-value.success {
  color: #28a745;
}

.stat-value.failed {
  color: #dc3545;
}

/* 通用样式 */
.empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
}

.empty-state p {
  margin: 10px 0;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

.success-message {
  background: #d4edda;
  color: #155724;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #c3e6cb;
}

.warning-message {
  background: #fff3cd;
  color: #856404;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #ffeaa7;
}

.danger-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.danger-button:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.danger-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.info-button {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.info-button:hover {
  background: #138496;
  transform: translateY(-1px);
}

.info-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}
