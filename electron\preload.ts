import { ipc<PERSON>enderer, contextBridge } from 'electron'

// --------- Expose some API to the Renderer process ---------
contextBridge.exposeInMainWorld('electronAPI', {
  // 用户认证相关API
  userRegister: (userData: { username: string; email: string; password: string }) =>
    ipcRenderer.invoke('user-register', userData),

  userLogin: (credentials: { username: string; password: string }) =>
    ipcRenderer.invoke('user-login', credentials),

  // 服务器管理API
  addServer: (serverData: {
    userId: number;
    name: string;
    host: string;
    port: number;
    username: string;
    password: string;
    sshHost?: string;
    sshPort?: number;
    sshUsername?: string;
    sshPassword?: string;
    sshPrivateKey?: string;
  }) =>
    ipcRenderer.invoke('add-server', serverData),

  getServers: (userId: number) =>
    ipcRenderer.invoke('get-servers', userId),

  deleteServer: (serverId: number, userId: number) =>
    ipcRenderer.invoke('delete-server', { serverId, userId }),

  getServer: (serverId: number, userId: number) =>
    ipcRenderer.invoke('get-server', { serverId, userId }),

  updateServer: (serverId: number, userId: number, serverData: {
    userId: number;
    name: string;
    host: string;
    port: number;
    username: string;
    password: string;
    sshHost?: string;
    sshPort?: number;
    sshUsername?: string;
    sshPassword?: string;
    sshPrivateKey?: string;
  }) =>
    ipcRenderer.invoke('update-server', { serverId, userId, serverData }),

  testServerConnection: (serverConfig: {
    host: string;
    port: number;
    username: string;
    password: string;
    sshHost?: string;
    sshPort?: number;
    sshUsername?: string;
    sshPassword?: string;
    sshPrivateKey?: string;
  }) =>
    ipcRenderer.invoke('test-server-connection', serverConfig),

  updateServerStatus: (serverId: number, status: 'active' | 'inactive' | 'error') =>
    ipcRenderer.invoke('update-server-status', serverId, status),

  // 备份任务管理API
  createBackupTask: (taskData: {
    userId: number;
    serverId: number;
    name: string;
    databaseName: string;
    backupType: 'full' | 'incremental';
    scheduleType: 'manual' | 'hourly' | 'daily' | 'weekly' | 'monthly';
    scheduleTime?: string;
    scheduleDay?: number;
    backupPath: string;
    retentionDays: number;
  }) =>
    ipcRenderer.invoke('create-backup-task', taskData),

  getBackupTasks: (userId: number) =>
    ipcRenderer.invoke('get-backup-tasks', userId),

  deleteBackupTask: (taskId: number, userId: number) =>
    ipcRenderer.invoke('delete-backup-task', { taskId, userId }),

  // 备份历史API
  getBackupHistory: (userId: number, limit?: number) =>
    ipcRenderer.invoke('get-backup-history', userId, limit),

  getBackupTaskHistory: (taskId: number, userId: number) =>
    ipcRenderer.invoke('get-backup-task-history', taskId, userId),

  generateCompleteBackupSQL: (taskId: number, targetHistoryId: number, userId: number) =>
    ipcRenderer.invoke('generate-complete-backup-sql', taskId, targetHistoryId, userId),

  executeBackup: (taskId: number) =>
    ipcRenderer.invoke('execute-backup', taskId),

  getBackupFilePath: (historyId: number, userId: number) =>
    ipcRenderer.invoke('get-backup-file-path', historyId, userId),

  showItemInFolder: (filePath: string) =>
    ipcRenderer.invoke('show-item-in-folder', filePath),

  // 连接测试API
  testDatabaseExists: (serverConfig: {
    host: string;
    port: number;
    username: string;
    password: string;
    sshHost?: string;
    sshPort?: number;
    sshUsername?: string;
    sshPassword?: string;
    sshPrivateKey?: string;
  }, databaseName: string) =>
    ipcRenderer.invoke('test-database-exists', { serverConfig, databaseName }),

  testDatabaseConnection: (serverId: number, databaseName: string, userId: number) =>
    ipcRenderer.invoke('test-database-connection', serverId, databaseName, userId),

  // 系统信息API
  getSystemInfo: (userId: number) =>
    ipcRenderer.invoke('get-system-info', userId),

  getSystemMetrics: () =>
    ipcRenderer.invoke('get-system-metrics'),

  getBackupStats: (userId: number) =>
    ipcRenderer.invoke('get-backup-stats', userId),

  // 服务器端备份文件API
  getServerBackupFiles: (serverId: number, userId: number) =>
    ipcRenderer.invoke('get-server-backup-files', serverId, userId),

  downloadServerBackupFile: (serverId: number, filePath: string, userId: number) =>
    ipcRenderer.invoke('download-server-backup-file', serverId, filePath, userId),

  // 通用IPC方法
  on(...args: Parameters<typeof ipcRenderer.on>) {
    const [channel, listener] = args
    return ipcRenderer.on(channel, (event, ...args) => listener(event, ...args))
  },
  off(...args: Parameters<typeof ipcRenderer.off>) {
    const [channel, ...omit] = args
    return ipcRenderer.off(channel, ...omit)
  },
  send(...args: Parameters<typeof ipcRenderer.send>) {
    const [channel, ...omit] = args
    return ipcRenderer.send(channel, ...omit)
  },
  invoke(...args: Parameters<typeof ipcRenderer.invoke>) {
    const [channel, ...omit] = args
    return ipcRenderer.invoke(channel, ...omit)
  }
})
