import React, { useState, useEffect } from 'react';
import { BackupTaskData } from '../types/electron';
import ServerBackupFiles from './ServerBackupFiles';

interface BackupTask {
  id: number;
  name: string;
  database_name: string;
  backup_type: 'full' | 'incremental';
  schedule_type: 'manual' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'custom';
  schedule_time?: string;
  schedule_day?: number;
  custom_interval_minutes?: number;
  backup_path: string;
  retention_days: number;
  status: 'active' | 'inactive' | 'running' | 'error';
  last_backup_time?: string;
  last_backup_size: number;
  server_id: number;
  server_name: string;
  server_host: string;
  created_at: string;
}

interface Server {
  id: number;
  name: string;
  host: string;
  port: number;
}

interface BackupManagementProps {
  userId: number;
  onViewDetail?: (taskId: number) => void;
}

const BackupManagement: React.FC<BackupManagementProps> = ({ userId, onViewDetail }) => {
  const [tasks, setTasks] = useState<BackupTask[]>([]);
  const [servers, setServers] = useState<Server[]>([]);
  const [loading, setLoading] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingTask, setEditingTask] = useState<BackupTask | null>(null);
  const [testingDatabase, setTestingDatabase] = useState(false);
  const [executingBackup, setExecutingBackup] = useState<number | null>(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showServerFiles, setShowServerFiles] = useState(false);
  const [selectedServer, setSelectedServer] = useState<{ id: number; name: string } | null>(null);

  const [formData, setFormData] = useState({
    name: '',
    serverId: 0,
    databaseName: '',
    backupType: 'incremental' as 'full' | 'incremental',
    scheduleType: 'manual' as 'manual' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'custom',
    scheduleTime: '',
    scheduleDay: 1,
    customIntervalMinutes: 60,
    backupPath: '',
    retentionDays: 30
  });

  // 加载备份任务列表
  const loadTasks = async () => {
    try {
      setLoading(true);
      const result = await window.electronAPI.getBackupTasks(userId);
      if (result.success) {
        setTasks(result.tasks || []);
      } else {
        setError(result.message || '获取备份任务列表失败');
      }
    } catch (error) {
      console.error('获取备份任务列表失败:', error);
      setError('获取备份任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载服务器列表
  const loadServers = async () => {
    try {
      const result = await window.electronAPI.getServers(userId);
      if (result.success) {
        setServers(result.servers || []);
      }
    } catch (error) {
      console.error('获取服务器列表失败:', error);
    }
  };

  useEffect(() => {
    loadTasks();
    loadServers();
  }, [userId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'serverId' || name === 'scheduleDay' || name === 'retentionDays' 
        ? parseInt(value) || 0 
        : value
    }));
  };

  const handleTestDatabase = async () => {
    if (!formData.serverId || !formData.databaseName) {
      setError('请先选择服务器和输入数据库名称');
      return;
    }

    try {
      setTestingDatabase(true);
      setError('');
      setSuccess('');

      // 获取服务器配置
      const serverResult = await window.electronAPI.getServer(formData.serverId, userId);
      if (!serverResult.success || !serverResult.server) {
        setError('获取服务器配置失败');
        return;
      }

      const server = serverResult.server;
      const serverConfig = {
        host: server.host,
        port: server.port,
        username: server.username,
        password: server.password || '',
        sshHost: server.ssh_host,
        sshPort: server.ssh_port,
        sshUsername: server.ssh_username,
        sshPassword: server.ssh_password,
        sshPrivateKey: server.ssh_private_key
      };

      const result = await window.electronAPI.testDatabaseExists(serverConfig, formData.databaseName);

      if (result.success) {
        setSuccess(`数据库 '${formData.databaseName}' 连接成功！`);
      } else {
        setError(`数据库测试失败: ${result.message}`);
      }
    } catch (error) {
      console.error('测试数据库失败:', error);
      setError('测试数据库失败，请稍后重试');
    } finally {
      setTestingDatabase(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.serverId || !formData.databaseName || !formData.backupPath) {
      setError('请填写必填字段');
      return;
    }

    try {
      setLoading(true);
      setError('');
      setSuccess('');

      // 首先验证数据库是否存在
      setError('正在验证数据库连接...');
      console.log('Testing database connection with:', {
        serverId: formData.serverId,
        databaseName: formData.databaseName,
        userId: userId
      });

      const dbTestResult = await window.electronAPI.testDatabaseConnection(
        formData.serverId,
        formData.databaseName,
        userId
      );

      console.log('Database test result:', dbTestResult);

      if (!dbTestResult.success) {
        setError(`数据库验证失败: ${dbTestResult.message}`);
        setLoading(false);
        return;
      }

      setError('数据库验证成功，正在创建备份任务...');

      const taskData: BackupTaskData = {
        userId,
        serverId: formData.serverId,
        name: formData.name,
        databaseName: formData.databaseName,
        backupType: formData.backupType,
        scheduleType: formData.scheduleType,
        scheduleTime: formData.scheduleTime || undefined,
        scheduleDay: formData.scheduleDay || undefined,
        customIntervalMinutes: formData.scheduleType === 'custom' ? formData.customIntervalMinutes : undefined,
        backupPath: formData.backupPath,
        retentionDays: formData.retentionDays
      };

      console.log('Creating backup task with data:', taskData);

      let result;
      if (editingTask) {
        // 这里需要添加更新任务的API
        setError('任务编辑功能正在开发中');
        return;
      } else {
        result = await window.electronAPI.createBackupTask(taskData);
        console.log('Create backup task result:', result);
      }

      if (result.success) {
        setSuccess('备份任务创建成功');
        setFormData({
          name: '',
          serverId: 0,
          databaseName: '',
          backupType: 'incremental',
          scheduleType: 'manual',
          scheduleTime: '',
          scheduleDay: 1,
          customIntervalMinutes: 60,
          backupPath: '',
          retentionDays: 30
        });
        setShowAddForm(false);
        setEditingTask(null);
        loadTasks(); // 重新加载列表
      } else {
        // 显示详细的错误信息
        const errorMessage = result.message || '未知错误';
        console.error('创建备份任务失败:', {
          error: errorMessage,
          taskData: taskData,
          result: result
        });
        setError(`创建备份任务失败: ${errorMessage}`);
      }
    } catch (error: any) {
      console.error('创建备份任务异常:', error);
      const errorMessage = error?.message || error?.toString() || '未知异常';
      setError(`创建备份任务异常: ${errorMessage}。请检查网络连接和服务器配置。`);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (taskId: number) => {
    if (!confirm('确定要删除这个备份任务吗？')) {
      return;
    }

    try {
      setLoading(true);
      const result = await window.electronAPI.deleteBackupTask(taskId, userId);

      if (result.success) {
        setSuccess('备份任务删除成功');
        loadTasks(); // 重新加载列表
      } else {
        setError(result.message);
      }
    } catch (error) {
      console.error('删除备份任务失败:', error);
      setError('删除备份任务失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleExecuteBackup = async (taskId: number) => {
    if (!confirm('确定要立即执行这个备份任务吗？')) {
      return;
    }

    try {
      setExecutingBackup(taskId);
      setError('');
      setSuccess('');

      const result = await window.electronAPI.executeBackup(taskId);

      if (result.success) {
        setSuccess(`备份执行成功！文件已保存到: ${result.filePath}`);
        loadTasks(); // 重新加载列表以更新状态
      } else {
        setError(`备份执行失败: ${result.message}`);
      }
    } catch (error) {
      console.error('执行备份失败:', error);
      setError('执行备份失败，请稍后重试');
    } finally {
      setExecutingBackup(null);
    }
  };

  const handleToggleStatus = async (taskId: number, currentStatus: string) => {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';

    try {
      setLoading(true);
      // 这里需要添加更新任务状态的API
      setError('状态切换功能正在开发中');
      // 临时更新本地状态
      setTasks(prevTasks =>
        prevTasks.map(task =>
          task.id === taskId ? { ...task, status: newStatus as any } : task
        )
      );
      setSuccess(`任务已${newStatus === 'active' ? '启用' : '禁用'}`);
    } catch (error) {
      console.error('切换任务状态失败:', error);
      setError('切换任务状态失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 查看服务器备份文件
  const handleViewServerFiles = (serverId: number, serverName: string) => {
    setSelectedServer({ id: serverId, name: serverName });
    setShowServerFiles(true);
  };

  const handleRunNow = async (taskId: number) => {
    if (!confirm('确定要立即执行这个备份任务吗？')) {
      return;
    }

    try {
      setExecutingBackup(taskId);
      setError('');
      setSuccess('');

      const result = await window.electronAPI.executeBackup(taskId);

      if (result.success) {
        setSuccess(`备份执行成功！文件已保存到: ${result.filePath}`);
        loadTasks(); // 重新加载列表以更新状态
      } else {
        setError(`备份执行失败: ${result.message}`);
      }
    } catch (error) {
      console.error('执行备份失败:', error);
      setError('执行备份失败，请稍后重试');
    } finally {
      setExecutingBackup(null);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="backup-management">
      <div className="section-header">
        <h3>备份任务管理</h3>
        <button 
          className="primary-button"
          onClick={() => setShowAddForm(!showAddForm)}
          disabled={loading || servers.length === 0}
        >
          {showAddForm ? '取消' : '创建备份任务'}
        </button>
      </div>

      {servers.length === 0 && (
        <div className="warning-message">
          请先添加服务器后再创建备份任务
        </div>
      )}

      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}

      {showAddForm && servers.length > 0 && (
        <div className="add-task-form">
          <h4>创建备份任务</h4>
          <form onSubmit={handleSubmit}>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="name">任务名称 *</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="例如：用户数据库每日备份"
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="serverId">选择服务器 *</label>
                <select
                  id="serverId"
                  name="serverId"
                  value={formData.serverId}
                  onChange={handleInputChange}
                  required
                >
                  <option value={0}>请选择服务器</option>
                  {servers.map(server => (
                    <option key={server.id} value={server.id}>
                      {server.name} ({server.host}:{server.port})
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="databaseName">数据库名称 *</label>
                <div className="input-with-button">
                  <input
                    type="text"
                    id="databaseName"
                    name="databaseName"
                    value={formData.databaseName}
                    onChange={handleInputChange}
                    placeholder="要备份的数据库名称"
                    required
                  />
                  <button
                    type="button"
                    className="secondary-button"
                    onClick={handleTestDatabase}
                    disabled={!formData.serverId || !formData.databaseName || testingDatabase}
                  >
                    {testingDatabase ? '测试中...' : '测试连接'}
                  </button>
                </div>
              </div>
              <div className="form-group">
                <label htmlFor="backupType">备份类型</label>
                <select
                  id="backupType"
                  name="backupType"
                  value={formData.backupType}
                  onChange={handleInputChange}
                >
                  <option value="incremental">增量备份</option>
                  <option value="full">完整备份</option>
                </select>
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="scheduleType">调度类型</label>
                <select
                  id="scheduleType"
                  name="scheduleType"
                  value={formData.scheduleType}
                  onChange={handleInputChange}
                >
                  <option value="manual">手动执行</option>
                  <option value="hourly">每小时执行</option>
                  <option value="daily">每日执行</option>
                  <option value="weekly">每周执行</option>
                  <option value="monthly">每月执行</option>
                  <option value="custom">自定义间隔</option>
                </select>
              </div>
              {formData.scheduleType === 'custom' && (
                <div className="form-group">
                  <label htmlFor="customIntervalMinutes">备份间隔（分钟）</label>
                  <input
                    type="number"
                    id="customIntervalMinutes"
                    name="customIntervalMinutes"
                    value={formData.customIntervalMinutes}
                    onChange={handleInputChange}
                    min="1"
                    max="43200"
                    placeholder="60"
                  />
                  <small className="form-help">
                    设置备份间隔时间，单位为分钟。例如：60分钟 = 1小时，1440分钟 = 1天
                  </small>
                </div>
              )}
              {formData.scheduleType !== 'manual' && formData.scheduleType !== 'custom' && (
                <div className="form-group">
                  <label htmlFor="scheduleTime">
                    {formData.scheduleType === 'hourly' ? '执行分钟 (每小时的第几分钟)' : '执行时间'}
                  </label>
                  {formData.scheduleType === 'hourly' ? (
                    <input
                      type="number"
                      id="scheduleTime"
                      name="scheduleTime"
                      value={formData.scheduleTime ? parseInt(formData.scheduleTime.split(':')[1]) : 0}
                      onChange={(e) => {
                        const minute = parseInt(e.target.value) || 0;
                        setFormData(prev => ({
                          ...prev,
                          scheduleTime: `00:${minute.toString().padStart(2, '0')}`
                        }));
                      }}
                      min="0"
                      max="59"
                      placeholder="0"
                    />
                  ) : (
                    <input
                      type="time"
                      id="scheduleTime"
                      name="scheduleTime"
                      value={formData.scheduleTime}
                      onChange={handleInputChange}
                    />
                  )}
                </div>
              )}
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="backupPath">备份路径 *</label>
                <input
                  type="text"
                  id="backupPath"
                  name="backupPath"
                  value={formData.backupPath}
                  onChange={handleInputChange}
                  placeholder="/backup/mysql/"
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="retentionDays">保留天数</label>
                <input
                  type="number"
                  id="retentionDays"
                  name="retentionDays"
                  value={formData.retentionDays}
                  onChange={handleInputChange}
                  min="1"
                  max="365"
                />
              </div>
            </div>

            <div className="form-actions">
              <button type="submit" className="primary-button" disabled={loading}>
                {loading ? '创建中...' : '创建任务'}
              </button>
              <button 
                type="button" 
                className="secondary-button"
                onClick={() => setShowAddForm(false)}
                disabled={loading}
              >
                取消
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="tasks-list">
        {loading && !showAddForm && <div className="loading">加载中...</div>}
        
        {tasks.length === 0 && !loading ? (
          <div className="empty-state">
            <p>还没有创建任何备份任务</p>
            <p>点击"创建备份任务"按钮开始配置</p>
          </div>
        ) : (
          <div className="tasks-grid">
            {tasks.map(task => (
              <div key={task.id} className="task-card">
                <div className="task-header">
                  <h4>{task.name}</h4>
                  <span className={`status-badge ${task.status}`}>
                    {task.status === 'active' ? '活跃' : 
                     task.status === 'inactive' ? '未激活' : 
                     task.status === 'running' ? '运行中' : '错误'}
                  </span>
                </div>
                <div className="task-details">
                  <p><strong>服务器:</strong> {task.server_name} ({task.server_host})</p>
                  <p><strong>数据库:</strong> {task.database_name}</p>
                  <p><strong>备份类型:</strong> {task.backup_type === 'full' ? '完整备份' : '增量备份'}</p>
                  <p><strong>调度:</strong> {
                    task.schedule_type === 'manual' ? '手动执行' :
                    task.schedule_type === 'hourly' ? '每小时执行' :
                    task.schedule_type === 'daily' ? '每日执行' :
                    task.schedule_type === 'weekly' ? '每周执行' :
                    task.schedule_type === 'monthly' ? '每月执行' :
                    task.schedule_type === 'custom' ? `自定义间隔 (${task.custom_interval_minutes}分钟)` : '未知'
                  }</p>
                  <p><strong>备份路径:</strong> {task.backup_path}</p>
                  <p><strong>保留天数:</strong> {task.retention_days}天</p>
                  {task.last_backup_time && (
                    <>
                      <p><strong>最后备份:</strong> {new Date(task.last_backup_time).toLocaleString()}</p>
                      <p><strong>备份大小:</strong> {formatFileSize(task.last_backup_size)}</p>
                    </>
                  )}
                  <p><strong>创建时间:</strong> {new Date(task.created_at).toLocaleString()}</p>
                </div>
                <div className="task-actions">
                  <button
                    className="info-button"
                    onClick={() => onViewDetail?.(task.id)}
                  >
                    查看详情
                  </button>
                  <button
                    className="info-button"
                    onClick={() => handleViewServerFiles(task.server_id, task.server_name)}
                  >
                    服务器文件
                  </button>
                  <button
                    className={task.status === 'active' ? 'secondary-button' : 'primary-button'}
                    onClick={() => handleToggleStatus(task.id, task.status)}
                    disabled={loading}
                  >
                    {task.status === 'active' ? '禁用' : '启用'}
                  </button>
                  {task.status === 'active' && (
                    <button
                      className="primary-button"
                      onClick={() => handleRunNow(task.id)}
                      disabled={loading || executingBackup === task.id}
                    >
                      {executingBackup === task.id ? '备份中...' : '立即备份'}
                    </button>
                  )}
                  <button
                    className="danger-button"
                    onClick={() => handleDelete(task.id)}
                    disabled={loading}
                  >
                    删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 服务器备份文件弹窗 */}
      {showServerFiles && selectedServer && (
        <ServerBackupFiles
          userId={userId}
          serverId={selectedServer.id}
          serverName={selectedServer.name}
          onClose={() => {
            setShowServerFiles(false);
            setSelectedServer(null);
          }}
        />
      )}
    </div>
  );
};

export default BackupManagement;
