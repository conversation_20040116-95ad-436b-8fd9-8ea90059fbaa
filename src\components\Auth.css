.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: var(--bg-primary);
  padding: 20px;
}

.auth-card {
  background: var(--bg-secondary);
  border-radius: 8px;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-color);
  padding: 32px;
  width: 100%;
  max-width: 380px;
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-title {
  text-align: center;
  color: var(--text-primary);
  margin-bottom: 24px;
  font-size: 20px;
  font-weight: 600;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 13px;
}

.form-group input {
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s ease;
  outline: none;
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.form-group input:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 1px var(--border-focus);
}

.form-group input:disabled {
  background: var(--bg-tertiary);
  color: var(--text-disabled);
  cursor: not-allowed;
}

.form-group input::placeholder {
  color: var(--text-tertiary);
}

.error-message {
  background: #fef2f2;
  color: var(--danger-color);
  padding: 10px 12px;
  border-radius: 4px;
  border: 1px solid #fecaca;
  font-size: 13px;
  text-align: center;
}

.success-message {
  background: #f0fdf4;
  color: var(--success-color);
  padding: 10px 12px;
  border-radius: 4px;
  border: 1px solid #bbf7d0;
  font-size: 13px;
  text-align: center;
}

.auth-button {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
  min-height: 36px;
}

.auth-button:hover:not(:disabled) {
  background: var(--primary-hover);
}

.auth-button:active:not(:disabled) {
  background: var(--primary-active);
}

.auth-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.auth-switch {
  text-align: center;
  margin-top: 20px;
  color: var(--text-secondary);
  font-size: 13px;
}

.switch-button {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  font-weight: 500;
  margin-left: 4px;
  text-decoration: none;
  font-size: 13px;
  transition: color 0.2s ease;
  padding: 0;
}

.switch-button:hover:not(:disabled) {
  color: var(--primary-hover);
  text-decoration: underline;
}

.switch-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .auth-container {
    padding: 10px;
  }
  
  .auth-card {
    padding: 30px 20px;
  }
  
  .auth-title {
    font-size: 24px;
  }
}
