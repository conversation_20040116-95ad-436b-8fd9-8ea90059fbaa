/**
 * 企业级通知管理系统
 * 支持邮件、Webhook、短信等多种通知方式
 */

import * as nodemailer from 'nodemailer';
import * as fs from 'fs';
import * as path from 'path';
import { logger } from '../utils/Logger';
import { config } from '../config/AppConfig';

export interface NotificationTemplate {
  id: string;
  name: string;
  type: 'email' | 'webhook' | 'sms';
  subject?: string;
  htmlTemplate: string;
  textTemplate: string;
  variables: string[];
}

export interface NotificationRecipient {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  webhookUrl?: string;
  enabled: boolean;
  notificationTypes: string[];
}

export interface NotificationRequest {
  templateId: string;
  recipients: string[];
  variables: { [key: string]: any };
  priority: 'low' | 'normal' | 'high' | 'urgent';
  scheduledAt?: Date;
}

export interface NotificationLog {
  id: string;
  templateId: string;
  recipientId: string;
  type: 'email' | 'webhook' | 'sms';
  status: 'pending' | 'sent' | 'failed' | 'delivered';
  sentAt?: Date;
  deliveredAt?: Date;
  error?: string;
  retryCount: number;
  maxRetries: number;
}

/**
 * 通知管理器
 */
export class NotificationManager {
  private static instance: NotificationManager;
  private templates: Map<string, NotificationTemplate> = new Map();
  private recipients: Map<string, NotificationRecipient> = new Map();
  private notificationQueue: NotificationRequest[] = [];
  private notificationLogs: Map<string, NotificationLog> = new Map();
  private emailTransporter?: nodemailer.Transporter;
  private isProcessing = false;

  private constructor() {
    this.initializeEmailTransporter();
    this.initializeDefaultTemplates();
    this.startQueueProcessor();
  }

  public static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  /**
   * 初始化邮件传输器
   */
  private initializeEmailTransporter(): void {
    const emailConfig = config.get<any>('notification.email');
    
    if (emailConfig.enabled && emailConfig.smtp.host) {
      this.emailTransporter = nodemailer.createTransporter({
        host: emailConfig.smtp.host,
        port: emailConfig.smtp.port,
        secure: emailConfig.smtp.secure,
        auth: emailConfig.smtp.auth
      });

      // 验证邮件配置
      this.emailTransporter.verify((error, success) => {
        if (error) {
          logger.error('邮件配置验证失败', error);
        } else {
          logger.info('邮件服务已就绪');
        }
      });
    }
  }

  /**
   * 初始化默认模板
   */
  private initializeDefaultTemplates(): void {
    const defaultTemplates: NotificationTemplate[] = [
      {
        id: 'backup_success',
        name: '备份成功通知',
        type: 'email',
        subject: '备份任务执行成功 - {{taskName}}',
        htmlTemplate: `
          <h2>备份任务执行成功</h2>
          <p>任务名称: {{taskName}}</p>
          <p>数据库: {{databaseName}}</p>
          <p>备份类型: {{backupType}}</p>
          <p>文件大小: {{fileSize}}</p>
          <p>执行时间: {{duration}}</p>
          <p>完成时间: {{completedAt}}</p>
        `,
        textTemplate: `
          备份任务执行成功
          任务名称: {{taskName}}
          数据库: {{databaseName}}
          备份类型: {{backupType}}
          文件大小: {{fileSize}}
          执行时间: {{duration}}
          完成时间: {{completedAt}}
        `,
        variables: ['taskName', 'databaseName', 'backupType', 'fileSize', 'duration', 'completedAt']
      },
      {
        id: 'backup_failure',
        name: '备份失败通知',
        type: 'email',
        subject: '备份任务执行失败 - {{taskName}}',
        htmlTemplate: `
          <h2 style="color: red;">备份任务执行失败</h2>
          <p>任务名称: {{taskName}}</p>
          <p>数据库: {{databaseName}}</p>
          <p>备份类型: {{backupType}}</p>
          <p>错误信息: {{errorMessage}}</p>
          <p>失败时间: {{failedAt}}</p>
          <p>请检查系统日志获取详细信息。</p>
        `,
        textTemplate: `
          备份任务执行失败
          任务名称: {{taskName}}
          数据库: {{databaseName}}
          备份类型: {{backupType}}
          错误信息: {{errorMessage}}
          失败时间: {{failedAt}}
          请检查系统日志获取详细信息。
        `,
        variables: ['taskName', 'databaseName', 'backupType', 'errorMessage', 'failedAt']
      },
      {
        id: 'system_alert',
        name: '系统告警通知',
        type: 'email',
        subject: '系统告警 - {{alertTitle}}',
        htmlTemplate: `
          <h2 style="color: {{severityColor}};">系统告警</h2>
          <p>告警标题: {{alertTitle}}</p>
          <p>严重程度: {{severity}}</p>
          <p>告警信息: {{alertMessage}}</p>
          <p>触发时间: {{triggeredAt}}</p>
          <p>当前值: {{currentValue}}</p>
          <p>阈值: {{threshold}}</p>
        `,
        textTemplate: `
          系统告警
          告警标题: {{alertTitle}}
          严重程度: {{severity}}
          告警信息: {{alertMessage}}
          触发时间: {{triggeredAt}}
          当前值: {{currentValue}}
          阈值: {{threshold}}
        `,
        variables: ['alertTitle', 'severity', 'severityColor', 'alertMessage', 'triggeredAt', 'currentValue', 'threshold']
      },
      {
        id: 'user_login',
        name: '用户登录通知',
        type: 'email',
        subject: '用户登录通知',
        htmlTemplate: `
          <h2>用户登录通知</h2>
          <p>用户: {{username}}</p>
          <p>登录时间: {{loginTime}}</p>
          <p>IP地址: {{ipAddress}}</p>
          <p>用户代理: {{userAgent}}</p>
        `,
        textTemplate: `
          用户登录通知
          用户: {{username}}
          登录时间: {{loginTime}}
          IP地址: {{ipAddress}}
          用户代理: {{userAgent}}
        `,
        variables: ['username', 'loginTime', 'ipAddress', 'userAgent']
      }
    ];

    defaultTemplates.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  /**
   * 启动队列处理器
   */
  private startQueueProcessor(): void {
    setInterval(() => {
      if (!this.isProcessing && this.notificationQueue.length > 0) {
        this.processQueue();
      }
    }, 5000); // 每5秒检查一次队列
  }

  /**
   * 处理通知队列
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      while (this.notificationQueue.length > 0) {
        const request = this.notificationQueue.shift();
        if (request) {
          await this.processNotificationRequest(request);
        }
      }
    } catch (error) {
      logger.error('处理通知队列失败', error as Error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 处理单个通知请求
   */
  private async processNotificationRequest(request: NotificationRequest): Promise<void> {
    const template = this.templates.get(request.templateId);
    if (!template) {
      logger.error('通知模板不存在', { templateId: request.templateId });
      return;
    }

    for (const recipientId of request.recipients) {
      const recipient = this.recipients.get(recipientId);
      if (!recipient || !recipient.enabled) {
        continue;
      }

      const logId = this.generateLogId();
      const notificationLog: NotificationLog = {
        id: logId,
        templateId: request.templateId,
        recipientId,
        type: template.type,
        status: 'pending',
        retryCount: 0,
        maxRetries: 3
      };

      this.notificationLogs.set(logId, notificationLog);

      try {
        await this.sendNotification(template, recipient, request.variables, notificationLog);
      } catch (error) {
        logger.error('发送通知失败', error as Error, { logId, recipientId, templateId: request.templateId });
        notificationLog.status = 'failed';
        notificationLog.error = (error as Error).message;
      }
    }
  }

  /**
   * 发送通知
   */
  private async sendNotification(
    template: NotificationTemplate,
    recipient: NotificationRecipient,
    variables: { [key: string]: any },
    log: NotificationLog
  ): Promise<void> {
    switch (template.type) {
      case 'email':
        await this.sendEmailNotification(template, recipient, variables, log);
        break;
      case 'webhook':
        await this.sendWebhookNotification(template, recipient, variables, log);
        break;
      case 'sms':
        await this.sendSmsNotification(template, recipient, variables, log);
        break;
      default:
        throw new Error(`不支持的通知类型: ${template.type}`);
    }
  }

  /**
   * 发送邮件通知
   */
  private async sendEmailNotification(
    template: NotificationTemplate,
    recipient: NotificationRecipient,
    variables: { [key: string]: any },
    log: NotificationLog
  ): Promise<void> {
    if (!this.emailTransporter || !recipient.email) {
      throw new Error('邮件服务未配置或收件人邮箱为空');
    }

    const subject = this.renderTemplate(template.subject || '', variables);
    const html = this.renderTemplate(template.htmlTemplate, variables);
    const text = this.renderTemplate(template.textTemplate, variables);

    const mailOptions = {
      from: config.get<string>('notification.email.from'),
      to: recipient.email,
      subject,
      html,
      text
    };

    log.status = 'pending';
    log.sentAt = new Date();

    const info = await this.emailTransporter.sendMail(mailOptions);
    
    log.status = 'sent';
    logger.info('邮件通知发送成功', { 
      logId: log.id, 
      recipient: recipient.email, 
      messageId: info.messageId 
    });
  }

  /**
   * 发送Webhook通知
   */
  private async sendWebhookNotification(
    template: NotificationTemplate,
    recipient: NotificationRecipient,
    variables: { [key: string]: any },
    log: NotificationLog
  ): Promise<void> {
    if (!recipient.webhookUrl) {
      throw new Error('Webhook URL未配置');
    }

    const payload = {
      template: template.id,
      recipient: recipient.id,
      variables,
      timestamp: new Date().toISOString()
    };

    const response = await fetch(recipient.webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'BackupSystem-Notification/1.0'
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      throw new Error(`Webhook请求失败: ${response.status} ${response.statusText}`);
    }

    log.status = 'sent';
    log.sentAt = new Date();
    
    logger.info('Webhook通知发送成功', { 
      logId: log.id, 
      url: recipient.webhookUrl, 
      status: response.status 
    });
  }

  /**
   * 发送短信通知
   */
  private async sendSmsNotification(
    template: NotificationTemplate,
    recipient: NotificationRecipient,
    variables: { [key: string]: any },
    log: NotificationLog
  ): Promise<void> {
    // 短信服务实现（需要集成第三方短信服务）
    throw new Error('短信通知功能尚未实现');
  }

  /**
   * 渲染模板
   */
  private renderTemplate(template: string, variables: { [key: string]: any }): string {
    let rendered = template;
    
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      rendered = rendered.replace(regex, String(value));
    }
    
    return rendered;
  }

  /**
   * 发送通知
   */
  public async sendNotificationAsync(request: NotificationRequest): Promise<void> {
    // 检查是否需要立即发送
    if (request.priority === 'urgent') {
      await this.processNotificationRequest(request);
    } else {
      this.notificationQueue.push(request);
    }
  }

  /**
   * 添加通知模板
   */
  public addTemplate(template: NotificationTemplate): void {
    this.templates.set(template.id, template);
    logger.info('通知模板已添加', { templateId: template.id, name: template.name });
  }

  /**
   * 添加通知接收者
   */
  public addRecipient(recipient: NotificationRecipient): void {
    this.recipients.set(recipient.id, recipient);
    logger.info('通知接收者已添加', { recipientId: recipient.id, name: recipient.name });
  }

  /**
   * 获取通知日志
   */
  public getNotificationLogs(limit: number = 100): NotificationLog[] {
    return Array.from(this.notificationLogs.values())
      .sort((a, b) => (b.sentAt?.getTime() || 0) - (a.sentAt?.getTime() || 0))
      .slice(0, limit);
  }

  /**
   * 获取通知统计
   */
  public getNotificationStats(hours: number = 24): {
    total: number;
    sent: number;
    failed: number;
    pending: number;
  } {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    const logs = Array.from(this.notificationLogs.values())
      .filter(log => log.sentAt && log.sentAt >= cutoff);

    return {
      total: logs.length,
      sent: logs.filter(log => log.status === 'sent').length,
      failed: logs.filter(log => log.status === 'failed').length,
      pending: logs.filter(log => log.status === 'pending').length
    };
  }

  private generateLogId(): string {
    return 'notif_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}

export const notificationManager = NotificationManager.getInstance();
